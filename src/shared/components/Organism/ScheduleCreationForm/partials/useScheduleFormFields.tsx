import { useFormikContext } from 'formik';
import React, { useCallback, useMemo } from 'react';
import { type ComponentBuilderProps } from '@shared/uikit/Form/ComponentBuilder';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import { DefaultGroupOptions } from 'shared/constants/forms/defaultGroupOptions';
import { useMeetingFormFields } from 'shared/hooks/schedules/useMeetingFormFields';
import { useScheduleFormFieldOptions } from 'shared/hooks/schedules/useScheduleFormFieldOptions';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import Divider from 'shared/uikit/Divider';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import cnj from 'shared/uikit/utils/cnj';
import { schedulesDb, schedulesEventTypes } from 'shared/utils/constants/enums';

import useTranslation from 'shared/utils/hooks/useTranslation';
import useMedia from 'shared/uikit/utils/useMedia';
import { genTimeApiFunction } from 'shared/utils/form/form.util';
import AttendeePicker from './AttendeePicker.component';
import { getDatetimeType } from './getDatetimeType';
import classes from './useScheduleFormFields.module.scss';
import type { UserType } from '@shared/types/user';
import type { MeetingDetails } from 'shared/types/schedules/schedules';

const useScheduleFormFields = (
  permissions?: MeetingDetails['permissions'],
  targetAttendee?: UserType
) => {
  const { t } = useTranslation();
  const { isMoreThanTablet } = useMedia();
  const { authUser } = useGetAppObject();
  const { values, setFieldValue } = useFormikContext<any>();
  const isRemote = useMemo(
    () => values?.contactType?.value === schedulesDb.contactType[0].value,
    [values?.contactType?.value]
  );

  const {
    attachments,
    attendeePermissions,
    description,
    meetingChannel,
    meetingModel,
    meetingRemind,
    room,
  } = useMeetingFormFields(permissions);

  const { taskStatusOptions, assigneePermissionOption } =
    useScheduleFormFieldOptions();

  const invalidTimeFormat = useMemo(
    () => ({
      label: t('invalid_time_format'),
      value: '',
      noClick: true,
    }),
    [t]
  );

  const eventTitle = useCallback(
    (label: string) => ({
      label,
      required: true,
      name: 'title',
      cp: 'input',
      maxLength: 100,
      isFocused: true,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [permissions]
  );

  const meetingDate = useMemo(
    () => ({
      name: 'startDate',
      cp: 'datePicker',
      wrapStyle: cnj(classes.leftItem, classes.formItem),
      containerProps: {
        className: classes.datePickerRight,
      },
      variant: 'input',
      required: true,
      picker: 'date',
      containerWidth: isMoreThanTablet ? 360 : undefined,
      label: t('date'),
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, isMoreThanTablet, permissions]
  );
  const meetingTime = useMemo(
    () => ({
      name: 'startTime',
      wrapStyle: cnj(classes.rightItem, classes.formItem),
      label: t('time'),
      required: true,
      doNotUseTranslation: true,
      rightIconProps: {
        name: 'clock',
      },
      maxLength: 5,
      visibleRightIcon: true,
      cp: 'asyncAutoComplete',
      // format: "23:00"
      checkIsValid: (value: string) => /^(\d{0,2}(:\d{0,2})?)?$/.test(value),
      showDropDownWithoutEnteringAnything: true,
      initSearchValue: '',
      apiFunc: genTimeApiFunction({
        options: schedulesDb.timeOptions,
        invalidOption: invalidTimeFormat,
      }),
      showPreview: true,
      /**
       * Adds a colon after 2 digits
       */
      onChange: (value: any, { field }: any) => {
        if (field?.value?.label?.length === 1 && value?.label?.length === 2) {
          setTimeout(() => {
            setFieldValue('startTime', {
              value: null,
              label: value?.label?.concat(':'),
            });
          });
        }
      },
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [invalidTimeFormat, setFieldValue, t, permissions]
  );

  const datetimeType = useMemo(
    () =>
      getDatetimeType(
        {
          disabled: permissions && !permissions?.MODIFY_MEETING,
          fixedDatetimeGroups: [meetingDate, meetingTime],
          target: targetAttendee,
        },
        t
      ),
    [t, permissions, meetingDate, meetingTime]
  );

  const meetingDuration = useMemo(
    () => ({
      name: 'duration',
      cp: 'dropdownSelect',
      label: t('meeting_duration'),
      wrapStyle: classes.formItem,
      options: schedulesDb.meetingDurationOptions,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, permissions]
  );

  const location = useMemo<ComponentBuilderProps>(
    () => ({
      name: 'location',
      cp: 'cityPicker',
      apiParams: {
        countryCode: authUser?.location?.countryCode,
      },
      wrapStyle: classes.formItem,
      noExplicitEditButton: true,
      classNames: {
        itemWrapper: classes.noBottomMargin,
      },
      rightSideProps: {
        isFromRightSide: true,
        modalComponent: FixedRightSideModalDialog,
        modalComponentProps: {
          wide: isBusinessApp,
        },
        inputLabel: t('city'),
      },
      hideBack: false,
      hideClose: true,
      visibleHeaderBorderBottom: true,
      visibleRequiredHint: false,
      styles: {
        formRoot: classes.locationFormRoot,
        formWrap: classes.locationFormRootFields,
        submitButton: classes.maxWidth,
      },
      primaryAction: {
        label: t('add_location'),
      },
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, authUser?.location?.countryCode, permissions]
  );

  const roomGroup = useMemo(
    () => ({
      ...room,
      wrapStyle: classes.formItem,
    }),
    [room]
  );

  const timeZone = useMemo(
    () => ({
      label: t('time_zone'),
      wrapStyle: classes.formItem,
      ...DefaultGroupOptions.timeZone,
      disabledReadOnly: permissions && !permissions?.MODIFY_MEETING,
    }),
    [t, permissions]
  );

  const descriptionGroup = useMemo(
    () => ({
      ...description,
      wrapStyle: classes.formItem,
      className: classes.description,
    }),
    [description]
  );

  const attendees = useMemo(
    () => ({
      name: 'attendees',
      cp: (props: any) => (
        <AttendeePicker
          leftLabel={t('attendees')}
          buttonLabel={t('add_attendees')}
          accessLabel={t('modify_meeting')}
          creator={values?.creator || authUser}
          target={targetAttendee}
          permissionValue={schedulesDb.permissions.MODIFY_MEETING}
          {...props}
          className="mt-20"
        />
      ),
    }),
    [t, values?.creator, authUser, targetAttendee]
  );

  const assigneePermission = useMemo(
    () => ({
      ...attendeePermissions,
      name: 'assigneePermission',
      label: t('assigneePermission'),
      formGroup: {
        ...attendeePermissions.formGroup,
        title: t('assignee_permission'),
      },
      options: assigneePermissionOption,
      onChange: undefined,
    }),
    [t, assigneePermissionOption, attendeePermissions]
  );

  const meetingModelGroup = useMemo(
    () => ({
      ...meetingModel,
      wrapStyle: classes.formItem,
    }),
    [meetingModel]
  );
  const meetingRemindGroup = useMemo(
    () => ({
      ...meetingRemind,
      wrapStyle: classes.formItem,
    }),
    [meetingRemind]
  );

  const reminderDate = useMemo(
    () => ({
      ...meetingDate,
      name: 'startDate',
      wrapStyle: classes.formItem,
      label: t('date'),
      containerProps: {
        className: '',
      },
    }),
    [t, meetingDate]
  );
  const reminderTime = useMemo(
    () => ({
      ...meetingTime,
      name: 'startTime',
      label: t('time'),
      wrapStyle: cnj(classes.formItem),
    }),
    [t, meetingTime]
  );
  const allDay = useMemo(
    () => ({
      name: 'allDay',
      cp: 'checkBox',
      visibleOptionalLabel: false,
      label: t('just_date'),
      wrapStyle: cnj(classes.formItem),
    }),
    [t]
  );

  const taskStartDate = useMemo(
    () => ({
      ...meetingDate,
      wrapStyle: cnj(classes.leftItem, classes.formItem),
      label: t('start_date'),
      name: 'startDate',
    }),
    [t, meetingDate]
  );
  const taskStartTime = useMemo(
    () => ({
      ...meetingTime,
      wrapStyle: cnj(classes.rightItem, classes.formItem),
      label: t('start_time'),
      name: 'startTime',
    }),
    [t, meetingTime]
  );
  const taskEndDate = useMemo(
    () => ({
      ...meetingDate,
      minDate: new Date(values?.startDate),
      wrapStyle: cnj(classes.leftItem, classes.formItem),
      label: t('end_date'),
      name: 'endDate',
      containerProps: {
        className: classes.datePickerRight,
      },
      openTo: values?.allDay ? 'bottom-left' : 'bottom-right',
    }),
    [t, meetingDate, values?.startDate, values?.allDay]
  );

  const taskEndTime = useMemo(
    () => ({
      ...meetingTime,
      wrapStyle: cnj(classes.rightItem, classes.formItem),
      label: t('end_time'),
      name: 'endTime',
      apiFunc: genTimeApiFunction({
        options: schedulesDb.timeOptionsEnd,
        invalidOption: invalidTimeFormat,
      }),
      onChange: (value: any, { field }: any) => {
        if (field?.value?.label?.length === 1 && value?.label?.length === 2) {
          setTimeout(() => {
            setFieldValue('endTime', {
              value: null,
              label: value?.label?.concat(':'),
            });
          });
        }
      },
    }),
    [meetingTime, t, invalidTimeFormat, setFieldValue]
  );

  const taskCollaborators = useMemo(
    () => ({
      name: 'assignee',
      accessLabel: 'modify_task',
      cp: (props: any) => (
        <AttendeePicker
          leftLabel={t('assignees')}
          buttonLabel={t('add_assignee')}
          creator={authUser}
          permissionValue={schedulesDb.permissions.MODIFY_TASK}
          maxAttendees={2}
          className="mb-20"
          {...props}
        />
      ),
    }),
    [t, authUser]
  );

  const taskStatus = useMemo(
    () => ({
      name: 'status',
      label: t('status'),
      cp: 'dropdownSelect',
      required: true,
      options: taskStatusOptions,
      wrapStyle: classes.formItem,
    }),
    [t, taskStatusOptions]
  );

  const divider = useMemo(
    () => ({
      cp: () => <Divider className={classes.divider} />,
      name: 'divider',
    }),
    []
  );

  const meeting = useMemo(
    () =>
      [
        // creator,
        eventTitle(t('meeting_title')),
        timeZone,
        ...(targetAttendee ? [] : [meetingDate, meetingTime]),
        meetingModelGroup,
        ...(isRemote ? [] : [location, roomGroup]),
        meetingDuration,
        meetingRemindGroup,
        descriptionGroup,
        ...(targetAttendee ? [datetimeType] : []),
        // { ...divider, name: 'divider3' },
        meetingChannel,
        // { ...divider, name: 'divider4' },
        attendees,
        attendeePermissions,
        // { ...divider, name: 'divider5' },
        attachments,
      ].filter(Boolean),
    [
      targetAttendee,
      eventTitle,
      t,
      timeZone,
      meetingDate,
      meetingTime,
      meetingModelGroup,
      datetimeType,
      isRemote,
      location,
      roomGroup,
      meetingDuration,
      meetingRemindGroup,
      descriptionGroup,
      meetingChannel,
      attendees,
      attendeePermissions,
      attachments,
    ]
  );

  const reminder = useMemo(
    () => [
      eventTitle(t('reminder_title')),
      reminderDate,
      ...(!values?.allDay ? [reminderTime] : []),
      allDay,
      descriptionGroup,
      // reminderRepeat,
    ],
    [
      eventTitle,
      t,
      reminderDate,
      values?.allDay,
      reminderTime,
      allDay,
      descriptionGroup,
    ]
  );

  const task = useMemo(
    () => [
      // creator,
      eventTitle(t('task_title')),
      taskStartDate,
      ...(!values?.allDay ? [taskStartTime] : []),
      taskEndDate,
      ...(!values?.allDay ? [taskEndTime] : []),
      allDay,
      // reminderRepeat,
      descriptionGroup,
      divider,
      taskStatus,
      { ...divider, name: 'divider2' },
      taskCollaborators,
      assigneePermission,
      { ...divider, name: 'divider3' },
      attachments,
    ],
    [
      eventTitle,
      t,
      taskStartDate,
      values?.allDay,
      taskStartTime,
      taskEndDate,
      taskEndTime,
      allDay,
      descriptionGroup,
      divider,
      taskStatus,
      taskCollaborators,
      assigneePermission,
      attachments,
    ]
  );

  return useMemo(
    () => ({
      [schedulesEventTypes.MEETING]: meeting,
      [schedulesEventTypes.REMINDER]: reminder,
      [schedulesEventTypes.TASK]: task,
    }),
    [meeting, reminder, task]
  );
};

export default useScheduleFormFields;
