import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import Button from 'shared/uikit/Button';
import IconButton from 'shared/uikit/Button/IconButton';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import useTheme from 'shared/uikit/utils/useTheme';
import useTranslation from 'shared/utils/hooks/useTranslation';
import classes from './AttendeePicker.component.module.scss';
import { UserItem } from './UserItem';
import type { UserType } from 'shared/types/user';

export interface AttendeeUser extends UserType {
  permissions?: string[];
}
export interface AttendeeFormFieldProps {
  className?: string;
  leftLabel: string;
  buttonLabel: string;
  buttonIcon?: string;
  value?: AttendeeUser[] | AttendeeUser;
  creator?: AttendeeUser;
  target?: AttendeeUser;
  permissionValue?: string;
  maxAttendees?: number;
}

const AttendeeFormField: React.FC<AttendeeFormFieldProps> = ({
  className,
  value,
  leftLabel,
  buttonLabel,
  buttonIcon = 'plus',
  creator,
  target,
  permissionValue,
  maxAttendees = 100,
}) => {
  const { isDark } = useTheme();
  const { state, setScheduleEventsPanelData: setScheduleCreationModalData } =
    useSchedulesUrlState();
  const { t } = useTranslation();

  const onOpenAttendees = () => {
    setScheduleCreationModalData(
      {
        ...state.scheduleEventsPanelData,
        isInAttendeeSelection: true,
      },
      { replace: true }
    );
  };

  const isTaskAssignee =
    state.scheduleEventsPanelData?.schedulesEventType === 'TASK';
  const users = Array.isArray(value) ? value : value ? [value] : [];

  const allAttendees = [
    creator?.username ? creator : undefined,
    target?.username ? target : undefined,
    ...users,
  ].filter((a): a is AttendeeUser => !!a);

  const { values, setFieldValue } = useFormikContext<any>();
  const removeAttendee = useCallback(
    (user: AttendeeUser) => {
      const field = isTaskAssignee ? 'assignee' : 'attendees';
      setFieldValue(
        field,
        isTaskAssignee
          ? undefined
          : values?.attendees.filter(
              (attendee: AttendeeUser) => attendee.id !== user.id
            )
      );
    },
    [isTaskAssignee, setFieldValue, values?.attendees]
  );

  return (
    <Flex className={cnj(classes.attendeePickerComponentRoot, className)}>
      <Flex className={classes.titleWrapper}>
        <Typography color="smoke_coal" font="700" size={16} height={22}>
          {leftLabel}
        </Typography>
        <Typography color="border" font="500" size={13} height={15}>
          {`${allAttendees.length}/${maxAttendees}`}
        </Typography>
      </Flex>
      <Flex className={classes.usersWrapper}>
        {allAttendees.map((user) => {
          const isCreator = user?.username === creator?.username;
          const isTarget = user?.username === target?.username;
          const isActive =
            isCreator ||
            (permissionValue && user?.permissions?.includes(permissionValue));

          return (
            <Flex key={user.username} className={classes.userWrapper}>
              <UserItem
                user={user}
                role={isCreator ? t('creator') : isTarget ? t('candidate') : ''}
              />
              {!isCreator && !isTarget ? (
                <IconButton
                  colorSchema="transparentSmokeCoal"
                  name="times"
                  type="far"
                  size="md"
                  onClick={() => removeAttendee(user)}
                />
              ) : null}
            </Flex>
          );
        })}
      </Flex>
      <Button
        className="border border-techGray_20 border-dashed mt-12 !h-[56px] "
        onClick={onOpenAttendees}
        schema="transparent"
        label={buttonLabel}
        leftIcon={buttonIcon}
        leftType="far"
      />
    </Flex>
  );
};

export default AttendeeFormField;
