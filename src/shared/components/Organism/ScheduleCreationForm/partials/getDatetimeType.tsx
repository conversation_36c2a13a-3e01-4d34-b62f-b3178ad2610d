import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo, useState } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import AvailableHoursList from '@shared/uikit/AvailableHoursList/AvailableHoursList';
import Skeleton from '@shared/uikit/Skeleton';
import IconButton from '@shared/uikit/Button/IconButton';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import { Time } from '@shared/utils/Time';
import { MeetingDatetimeType } from 'shared/types/schedules/schedules';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import { useAvailability } from '../../AvailabilityModule/partials/useAvailability';
import { useDatePicker } from '../../DatePicker/useDatePicker';
import { DatepickerV3 } from '../../DatepickerV3';
import type { Timezone, WeekDay } from '@shared/types/preferences';
import type { UserType } from '@shared/types/user';
import type { Dayjs } from 'dayjs';
import type { DateType } from 'shared/types/schedules/schedules';

interface ArgsType {
  fixedDatetimeGroups: any[];
  target: UserType;
  disabled?: boolean;
}

export function getDatetimeType(args: ArgsType, t = (s: string) => s) {
  const { disabled, fixedDatetimeGroups = [], target } = args;
  const options = [
    {
      label: t('share_availability'),
      value: MeetingDatetimeType.PROVIDE_AVAILABILITY,
      children: <ProvideAvailability />,
    },
    {
      label: t('member_availability'),
      value: MeetingDatetimeType.CONSUME_AVAILABILITY,
      children: <ConsumeAvailability target={target} />,
    },
    {
      label: t('specific_date&time'),
      value: MeetingDatetimeType.FIXED_TIME,
      children: <DynamicFormBuilder groups={fixedDatetimeGroups} />,
    },
  ];

  return {
    formGroup: {
      color: 'smoke_coal',
      title: t('date_n_time'),
      className: '!my-0',
    },
    cp: 'radioGroup',
    name: 'datetimeType',
    options,
    classNames: {
      itemWrapper: 'w-full',
    },
    label: t('meeting_channel'),
    disabledReadOnly: disabled,
  };
}

const ProvideAvailability = () => {
  const { t } = useTranslation();
  const { timesheets } = useAvailability();

  const options = useMemo(
    () => timesheets.map(({ id, title }) => ({ value: id, label: title })),
    [timesheets]
  );

  const selectAvailability = useMemo(
    () => ({
      name: 'availability',
      cp: 'dropdownSelect',
      label: t('choose_availability'),
      required: true,
      options,
    }),
    [options, t]
  );

  const group = useMemo(() => [selectAvailability], [selectAvailability]);

  return <DynamicFormBuilder groups={group} />;
};

const ConsumeAvailability = ({ target }: { target: UserType }) => {
  const { t } = useTranslation();

  const { values } = useFormikContext<{ timezone?: Timezone }>();
  const [timesheetId, setTimesheetId] = useState('');

  const [page, setPage] = useState<'details' | 'hours' | 'display'>('details');
  const [selectedDay, setSelectedDay] = useState<Dayjs>();

  const {
    selectedHour,
    setSelectedHour,
    isLoadingOtherUserTimesheets,
    displayingTimezone,
    displayingTimesheet,
    timesheets,
    timesheet,
  } = useAvailability({
    userId: target.id,
    timesheetId,
  });

  useEffect(() => {
    if (timesheets.length && !timesheetId) {
      setTimesheetId(timesheets[0].id);
    }
  }, [timesheets]);

  const availableWeekDays = Object.keys(
    displayingTimesheet?.dailyHours ?? {}
  ) as WeekDay[];

  const { localviewDate, setLocalviewDate, initialViewDate } = useDatePicker({
    timezone: values.timezone,
  });

  const onOpenHours = useCallback((date: Dayjs) => {
    setSelectedDay(date);
    setPage('hours');
  }, []);

  const onHourClick = useCallback(
    (date: Dayjs) => {
      setSelectedDay(date);
      setPage('display');
    },
    [setSelectedHour]
  );

  const handleCellClick = (date: DateType) => {
    setLocalviewDate(Time.getViewDateDetails(date));
    onOpenHours?.(date);
  };
  const handleLocalDateChange = (date: DateType) => {
    setLocalviewDate(Time.getViewDateDetails(date));
  };

  const display = (
    <Flex flexDir="row" className="justify-between items-center !mb-12">
      <Flex>
        <Typography color="colorIconForth2" size={13} font="500">
          {t('date')}
        </Typography>
        <Typography color="smoke_coal" size={14} font="400">
          {selectedDay
            ? Time.getTimeWithOffset(
                selectedDay,
                displayingTimezone?.offset
                  ? Number(displayingTimezone.offset)
                  : 0
              ).format(page === 'hours' ? 'dddd, ll' : 'dddd, ll [at] LT')
            : null}
        </Typography>
      </Flex>
      <IconButton
        name="replace"
        type="far"
        variant="rectangle"
        colorSchema="graySecondary"
        onClick={() => setPage('details')}
      />
    </Flex>
  );

  if (page === 'details') {
    return displayingTimezone ? (
      <CardWrapper
        classNames={{
          root: 'border border-solid !border-techGray_20',
          container: '!p-16',
        }}
      >
        {timesheet ? (
          <DatepickerV3
            localviewDate={localviewDate}
            data={{
              filteredEvents: [],
              savedEvents: [],
              viewDate: initialViewDate,
              availableWeekDays,
            }}
            disableDefaultBehaviour
            onCellClick={handleCellClick}
            onDateChange={handleLocalDateChange}
            disableSelectionOfPastTime
            doNotShowActivesOfAdjacentMonths
            numberOfCells={6 * 7}
          />
        ) : isLoadingOtherUserTimesheets ? (
          <Skeleton className="rounded !h-[342px]" />
        ) : (
          <EmptySectionInModules
            classNames={{ container: 'my-auto' }}
            title={t('availability_not_found')}
            text={t('availability_not_found_desc')}
          />
        )}
      </CardWrapper>
    ) : null;
  }

  if (page === 'hours') {
    return (
      selectedDay &&
      displayingTimesheet && (
        <>
          {display}
          <AvailableHoursList
            timesheet={displayingTimesheet}
            day={selectedDay}
            isSelectMode
            // bookedMeetings={bookedMeetings}
            onHourClick={onHourClick}
            classNames={{
              container: '!gap-8 !grid !grid-cols-6 !m-0',
              // button: '!w-1/6',
            }}
          />
        </>
      )
    );
  }

  return display;
};
