import { useState } from 'react';
import { useTemplateList } from '@shared/components/Organism/AutomationModal/hooks/useTemplateList';
import useResponseToast from '@shared/hooks/useResponseToast';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import {
  createEmailTemplate,
  updateEmailTemplate,
  setEmailTemplateDefault,
  removeEmailTemplateDefault,
  deleteEmailTemplate,
  type CreateEmailTemplateRequest,
} from '@shared/utils/api/template';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type {
  TemplateFormData,
  NormalizedTemplate,
  UseTemplateActionsOptions,
  DuplicatedText,
} from '../types/template.types';

export const useTemplateActions = (
  options: UseTemplateActionsOptions = {},
  duplicatedText: DuplicatedText
) => {
  const {
    onTemplateCreated,
    onTemplateUpdated,
    onTemplateDeleted,
    onDefaultChanged,
    confirmDelete = true,
  } = options;

  const { handleSuccess } = useResponseToast();

  const { t } = useTranslation();
  const [isUpdatingDefault, setIsUpdatingDefault] = useState<string | null>(
    null
  );

  const templateList = useTemplateList({
    searchEnabled: true,
  });

  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });

  const { mutate: deleteEmailTemplateMutation, isPending: isDeletePending } =
    useReactMutation({
      apiFunc: (id: number) => deleteEmailTemplate(id),
      onSuccess: () => {
        templateList.refetch();
      },
    });

  const { mutate: createEmailTemplateMutation, isPending: isCreatePending } =
    useReactMutation({
      apiFunc: (templateData: CreateEmailTemplateRequest) =>
        createEmailTemplate(templateData),
      onSuccess: () => {
        templateList.refetch();
      },
    });

  const { mutate: updateEmailTemplateMutation, isPending: isUpdatePending } =
    useReactMutation({
      apiFunc: ({
        id,
        templateData,
      }: {
        id: number;
        templateData: CreateEmailTemplateRequest;
      }) => updateEmailTemplate(id, templateData),
      onSuccess: () => {
        templateList.refetch();
      },
    });

  const { mutate: setDefaultMutation, isPending: isSetDefaultPending } =
    useReactMutation({
      apiFunc: (id: number) => setEmailTemplateDefault(id),
      onSuccess: () => {
        templateList.refetch();
      },
    });

  const { mutate: removeDefaultMutation } = useReactMutation({
    apiFunc: (id: number) => removeEmailTemplateDefault(id),
    onSuccess: () => {
      templateList.refetch();
    },
  });

  const transformFormDataToApi = (
    data: TemplateFormData
  ): CreateEmailTemplateRequest => ({
    title: data.templateName,
    subject: data.subject,
    message: data.message,
    timeDelay: data.delay === 'IMMEDIATELY' ? 'IMMEDIATELY' : data.delay,
    fileIds: (data.attachments || []).map((attachment: any) =>
      typeof attachment === 'number'
        ? attachment
        : parseInt(attachment.id || attachment, 10)
    ),
    hasFollowup: data.hasFollowup,
    ...(data.hasFollowup && {
      followupTitle: data.followupTitle,
      followupMessage: data.followupMessage,
      followupPeriod: data.followupPeriod,
      followupFileIds: (data.followupAttachments || []).map(
        (attachment: any) =>
          typeof attachment === 'number'
            ? attachment
            : parseInt(attachment.id || attachment, 10)
      ),
    }),
  });

  const createTemplate = (
    data: TemplateFormData,
    handleSuccess?: () => void
  ) => {
    const apiData = transformFormDataToApi(data);

    createEmailTemplateMutation(apiData, {
      onSuccess: (result: any) => {
        const normalizedTemplate: NormalizedTemplate = {
          id: result.id.toString(),
          title: result.title || '',
          subject: result.subject || '',
          message: result.message || '',
          fileIds: (result.fileIds || []).map((id: any) => id.toString()),
          hasFollowup: result.hasFollowup || false,
          default: result.default || false,
        };

        onTemplateCreated?.(normalizedTemplate);
        handleSuccess?.();
      },
    });
  };

  const updateTemplate = (
    templateId: number,
    data: TemplateFormData,
    handleSuccess?: () => void
  ) => {
    const apiData = transformFormDataToApi(data);

    updateEmailTemplateMutation(
      { id: templateId, templateData: apiData },
      {
        onSuccess: (result: any) => {
          const normalizedTemplate: NormalizedTemplate = {
            id: result.id.toString(),
            title: result.title || '',
            subject: result.subject || '',
            message: result.message || '',
            fileIds: (result.fileIds || []).map((id: any) => id.toString()),
            hasFollowup: result.hasFollowup || false,
            default: result.default || false,
          };

          onTemplateUpdated?.(normalizedTemplate);
          handleSuccess?.();
        },
      }
    );
  };

  const deleteTemplate = (template: NormalizedTemplate) => {
    const performDelete = () => {
      deleteEmailTemplateMutation(parseInt(template.id, 10), {
        onSuccess: () => {
          onTemplateDeleted?.(template.id);
        },
      });
    };

    if (confirmDelete) {
      openConfirmDialog({
        title: t('delete_template'),
        message: t('delete_template_confirm'),
        confirmButtonText: t('delete'),
        cancelButtonText: t('cancel'),
        isAjaxCall: true,
        apiProps: {
          func: performDelete,
        },
      });

      return true;
    }

    return performDelete();
  };

  const setDefaultTemplate = (
    templateId: string,
    isCurrentlyDefault: boolean
  ) => {
    if (isUpdatingDefault === templateId) {
      return false;
    }

    setIsUpdatingDefault(templateId);
    const id = parseInt(templateId, 10);

    const mutation = isCurrentlyDefault
      ? removeDefaultMutation
      : setDefaultMutation;

    return mutation(id, {
      onSuccess: () => {
        onDefaultChanged?.(templateId, isCurrentlyDefault);
        setIsUpdatingDefault(null);
      },
      onError: () => {
        setIsUpdatingDefault(null);
      },
    });
  };

  const duplicateTemplate = (template: NormalizedTemplate) => {
    const duplicateData: TemplateFormData = {
      templateName: `${template.title}`,
      subject: template.subject,
      message: template.message,
      delay: 'IMMEDIATELY',
      attachments: template.fileIds.map((id) => ({ id })),
      hasFollowup: template.hasFollowup,
      followupPeriod: template.followupPeriod || '',
      followupTitle: template.followupTitle || '',
      followupMessage: template.followupMessage || '',
      followupAttachments: template.followupAttachments || [],
    };

    return createTemplate(duplicateData, () => {
      handleSuccess({
        message: duplicatedText.message,
        title: duplicatedText.title,
      });
    });
  };

  const isMutating =
    isDeletePending ||
    isCreatePending ||
    isUpdatePending ||
    isSetDefaultPending;

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    setDefaultTemplate,
    duplicateTemplate,
    isMutating,
    isUpdatingDefault,
    transformFormDataToApi,
  };
};
