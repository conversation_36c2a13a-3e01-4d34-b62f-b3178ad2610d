import type { EmailTemplateResponse } from '@shared/utils/api/template';

export interface TemplateFormData {
  templateName: string;
  subject: string;
  message: string;
  delay: string;
  attachments: any[];
  hasFollowup: boolean;
  followupPeriod: string;
  followupTitle: string;
  followupMessage: string;
  followupAttachments: any[];
  default?: boolean;
}

export interface NormalizedTemplate {
  id: string;
  title: string;
  subject: string;
  message: string;
  fileIds: string[];
  hasFollowup: boolean;
  default: boolean;
  followupPeriod?: string;
  followupTitle?: string;
  followupMessage?: string;
  followupAttachments?: any[];
}

export interface TemplateListConfig {
  searchPlaceholder?: string;
  showSearch?: boolean;
  showActions?: boolean;
  showDefaultToggle?: boolean;
  emptyStateMessage?: string;
}

export interface TemplateActionConfig {
  showDuplicate?: boolean;
  showEdit?: boolean;
  showDelete?: boolean;
  showSetDefault?: boolean;
  customActions?: TemplateAction[];
}

export interface TemplateAction {
  id: string;
  icon: string;
  title: string;
  iconType?: string;
  onClick: (template: NormalizedTemplate) => void;
}

export interface TemplateFormConfig {
  showDelay?: boolean;
  showFollowup?: boolean;
  showAttachments?: boolean;
  readOnly?: boolean;
  customFields?: any[];
}

export interface UseTemplateListOptions {
  searchEnabled?: boolean;
  defaultSearchQuery?: string;
  transformTemplate?: (template: any) => NormalizedTemplate;
  onSuccess?: ((data: EmailTemplateResponse[]) => void) | undefined;
}

export interface UseTemplateActionsOptions {
  onTemplateCreated?: (template: NormalizedTemplate) => void;
  onTemplateUpdated?: (template: NormalizedTemplate) => void;
  onTemplateDeleted?: (templateId: string) => void;
  onDefaultChanged?: (templateId: string, isDefault: boolean) => void;
  confirmDelete?: boolean;
}

export interface DuplicatedText {
  title: string;
  message: string;
}

export interface UseTemplateFormOptions {
  initialData?: TemplateFormData;
  onFormChange?: (values: TemplateFormData, isValid: boolean) => void;
  onSubmit?: (data: TemplateFormData, isCreate?: boolean) => void;
  config?: TemplateFormConfig;
}

export interface TemplateListProps {
  templates: NormalizedTemplate[];
  isLoading?: boolean;
  searchQuery?: string;
  defaultTemplateId?: string;
  onSearchChange?: (query: string) => void;
  onTemplateClick?: (templateId: string) => void;
  onSetDefault?: (
    templateId: string,
    isDefault: boolean,
    e?: React.MouseEvent
  ) => void;
  actions?: TemplateAction[];
  config?: TemplateListConfig;
  isUpdatingDefault?: string | null;
  isDefaultTemplateLoading?: boolean;
}

export interface TemplateSearchProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export interface TemplateActionsProps {
  template: NormalizedTemplate;
  actions: TemplateAction[];
  isOpen?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
}

export const DELAY_OPTIONS = [
  { label: 'Immediately', value: 'IMMEDIATELY' },
  { label: '10 minutes', value: '10_MINUTES' },
  { label: '30 minutes', value: '30_MINUTES' },
  { label: '2 hours', value: '2_HOURS' },
  { label: '5 hours', value: '5_HOURS' },
  { label: '10 Hours', value: '10_HOURS' },
  { label: '12 Hours', value: '12_HOURS' },
  { label: '24 Hours', value: '24_HOURS' },
  { label: '48 Hours', value: '48_HOURS' },
] as const;

export const FOLLOWUP_PERIOD_OPTIONS = [
  { label: '3 days', value: '_3_DAYS' },
  { label: '5 days', value: '_5_DAYS' },
  { label: '7 days', value: '_7_DAYS' },
  { label: '14 days', value: '_14_DAYS' },
] as const;

export type DelayValue = (typeof DELAY_OPTIONS)[number]['value'];
export type FollowupPeriodValue =
  (typeof FOLLOWUP_PERIOD_OPTIONS)[number]['value'];
