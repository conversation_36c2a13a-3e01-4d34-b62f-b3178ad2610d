import React from 'react';
import { useMultiStepFormState } from '@shared/hooks/useMultiStepForm';
import AutoInterviewModal from './AutoInterviewModal';
import AutomationModal from './AutomationModal';
import AutoMessageModal from './AutoMessageModal';
import AutoMoveModal from './AutoMoveModal';
import AutoNotesModal from './AutoNotesModal';
import AutoReplyModal from './AutoReplyModal';
import AutoTodoModal from './AutoTodoModal';
import RejectionModal from './RejectionModal';
import RequirementsModal from './RequirementsModal';

const AutomationModalRouter: React.FC = () => {
  const automationState = useMultiStepFormState('automation');

  if (!automationState?.isOpen) {
    return null;
  }

  const modalType = automationState.type || 'main';

  switch (modalType) {
    case 'main':
      return <AutomationModal />;
    case 'autoMove':
      return <AutoMoveModal />;
    case 'requirements':
      return <RequirementsModal />;
    case 'autoReply':
      return <AutoReplyModal />;
    case 'autoMessage':
      return <AutoMessageModal />;
    case 'autoInterview':
      return <AutoInterviewModal />;
    case 'autoNotes':
      return <AutoNotesModal />;
    case 'autoTodo':
      return <AutoTodoModal />;
    case 'rejection':
      return <RejectionModal />;
    default:
      return <AutomationModal />;
  }
};

export default AutomationModalRouter;
