import React, { useState, useCallback } from 'react';
import type { PaginateResponse } from '@shared/types/response';
import type {
  EmailTemplateResponse,
  EmailTemplateSearchParams,
} from '../api/template';
import { getEmailTemplates } from '../api/template';
import useReactQuery from './useReactQuery';
import { QueryKeys } from '../constants';

type EmailTemplates = {
  onSuccess?: ((data: EmailTemplateResponse[]) => void) | undefined;
};

export default function useEmailTemplates({ onSuccess }: EmailTemplates) {
  const [searchParams, setSearchParams] = useState<EmailTemplateSearchParams>({
    page: 0,
    size: 10,
  });

  const emailTemplatesQuery = useReactQuery<
    PaginateResponse<EmailTemplateResponse>
  >({
    action: {
      key: [QueryKeys.getEmailTemplates, searchParams],
      apiFunc: () => getEmailTemplates(searchParams),
    },
    config: {
      onSuccess: (template) => {
        onSuccess?.(template?.content);
      },
    },
  });

  const updateSearchParams = useCallback(
    (newParams: Partial<EmailTemplateSearchParams>) => {
      setSearchParams((prev) => ({
        ...prev,
        ...newParams,
      }));
    },
    []
  );

  return {
    ...emailTemplatesQuery,
    templates: emailTemplatesQuery.data?.content || [],
    totalElements: emailTemplatesQuery.data?.totalElements || 0,
    totalPages: emailTemplatesQuery.data?.totalPages || 0,
    searchParams,
    updateSearchParams,
  };
}
