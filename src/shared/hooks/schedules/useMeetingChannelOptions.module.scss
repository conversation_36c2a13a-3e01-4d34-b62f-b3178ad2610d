@use 'sass:math';

@import '/src/shared/theme/theme.scss';

@layer organism {
  .marginTop {
    margin-top: variables(xLargeGutter) * 0.25;
  }
  .cLButton {
    margin-top: variables(xLargeGutter) * 0.5;
    padding-left: 0;
    padding-right: 0;
  }

  .textInput {
    margin-top: 6px;
  }
  .dropdown {
    margin-top: variables(xLargeGutter) * 0.25;
  }
  .tooltipWrapper {
    max-width: variables(rightPaneMaxWidth);
    padding: variables(gutter) * 0.5;
    text-wrap: wrap;
    border-radius: variables(gutter) * 0.5;
  }
  .checkboxGroup {
    padding-inline: variables(gutter) * 0.5;
  }
  .checkboxContainer {
    margin-inline: math.div(variables(gutter), -2);
    width: 100%;
  }
  .formGroupHeader {
    background-color: transparent;
    margin-bottom: 6px;
    padding: 0;
  }

  .formGroupRoot {
    padding: 6px 0;
  }

  @media (min-width: breakpoints(tablet)) {
  }
}
